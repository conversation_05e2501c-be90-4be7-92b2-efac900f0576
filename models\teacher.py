# -*- coding: utf-8 -*-
"""
教师数据模型
提供教师表的CRUD操作方法
"""

from utils.database_helper import db_helper
import logging

class TeacherModel:
    """教师数据模型类"""
    
    def __init__(self):
        self.table_name = "Teacher"
        self.logger = logging.getLogger(__name__)
    
    def get_all_paginated(self, page=1, page_size=10, search_term=""):
        """
        获取分页的教师数据
        
        Args:
            page (int): 页码
            page_size (int): 每页记录数
            search_term (str): 搜索关键词
            
        Returns:
            dict: 分页数据结果
        """
        try:
            where_clause = ""
            if search_term:
                where_clause = f"Tname LIKE '%{search_term}%' OR Sdept LIKE '%{search_term}%'"
            
            return db_helper.get_paginated_data(
                self.table_name, 
                page, 
                page_size, 
                where_clause, 
                "Tno"
            )
        except Exception as e:
            self.logger.error(f"获取教师分页数据失败: {str(e)}")
            raise
    
    def get_by_id(self, tno):
        """
        根据教师编号获取教师信息
        
        Args:
            tno (str): 教师编号
            
        Returns:
            dict: 教师信息字典，如果不存在返回None
        """
        try:
            sql = "SELECT * FROM Teacher WHERE Tno = ?"
            result = db_helper.execute_query(sql, (tno,))
            return result[0] if result else None
        except Exception as e:
            self.logger.error(f"获取教师信息失败: {str(e)}")
            raise
    
    def create(self, teacher_data):
        """
        创建新教师记录
        
        Args:
            teacher_data (dict): 教师信息字典
            
        Returns:
            bool: 创建成功返回True
        """
        try:
            sql = """
                INSERT INTO Teacher (Tno, Tpassword, Tname, Sdept, Duty, Telephone, Book)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            """
            params = (
                str(teacher_data['Tno']),  # 确保教师编号为字符串
                str(teacher_data['Tpassword']),  # 确保密码为字符串
                str(teacher_data['Tname']),  # 确保姓名为字符串
                str(teacher_data['Sdept']),  # 确保系别为字符串
                str(teacher_data.get('Duty', '')),  # 确保职务为字符串
                str(teacher_data.get('Telephone', '')),  # 确保电话为字符串
                str(teacher_data.get('Book', ''))  # 确保教材为字符串
            )
            
            affected_rows = db_helper.execute_non_query(sql, params)
            return affected_rows > 0
            
        except Exception as e:
            self.logger.error(f"创建教师记录失败: {str(e)}")
            raise
    
    def update(self, tno, teacher_data):
        """
        更新教师信息
        
        Args:
            tno (str): 教师编号
            teacher_data (dict): 更新的教师信息
            
        Returns:
            bool: 更新成功返回True
        """
        try:
            sql = """
                UPDATE Teacher 
                SET Tpassword=?, Tname=?, Sdept=?, Duty=?, Telephone=?, Book=?
                WHERE Tno=?
            """
            params = (
                str(teacher_data['Tpassword']),  # 确保密码为字符串
                str(teacher_data['Tname']),  # 确保姓名为字符串
                str(teacher_data['Sdept']),  # 确保系别为字符串
                str(teacher_data.get('Duty', '')),  # 确保职务为字符串
                str(teacher_data.get('Telephone', '')),  # 确保电话为字符串
                str(teacher_data.get('Book', '')),  # 确保教材为字符串
                str(tno)  # 确保教师编号为字符串
            )
            
            affected_rows = db_helper.execute_non_query(sql, params)
            return affected_rows > 0
            
        except Exception as e:
            self.logger.error(f"更新教师信息失败: {str(e)}")
            raise
    
    def delete(self, tno):
        """
        删除教师记录
        
        Args:
            tno (str): 教师编号
            
        Returns:
            bool: 删除成功返回True
        """
        try:
            # 检查是否有关联的课程
            check_sql = "SELECT COUNT(*) as count FROM Course WHERE Tno = ?"
            result = db_helper.execute_query(check_sql, (tno,))
            if result[0]['count'] > 0:
                raise Exception("无法删除：该教师还有关联的课程记录")
            
            sql = "DELETE FROM Teacher WHERE Tno = ?"
            affected_rows = db_helper.execute_non_query(sql, (tno,))
            return affected_rows > 0
            
        except Exception as e:
            self.logger.error(f"删除教师记录失败: {str(e)}")
            raise

# 全局教师模型实例
teacher_model = TeacherModel()
