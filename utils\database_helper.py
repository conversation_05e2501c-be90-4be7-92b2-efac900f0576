# -*- coding: utf-8 -*-
"""
数据库助手工具
提供通用的数据库操作方法和分页查询功能
"""

import pyodbc
from config.database import db_config
import logging

class DatabaseHelper:
    """数据库操作助手类"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
    
    def execute_query(self, sql, params=None):
        """
        执行查询SQL语句
        
        Args:
            sql (str): SQL查询语句
            params (tuple): SQL参数
            
        Returns:
            list: 查询结果列表
        """
        try:
            conn = db_config.get_connection()
            cursor = conn.cursor()
            
            if params:
                cursor.execute(sql, params)
            else:
                cursor.execute(sql)
            
            # 获取列名
            columns = [column[0] for column in cursor.description]
            
            # 获取数据并转换为字典列表
            rows = cursor.fetchall()
            result = []
            for row in rows:
                result.append(dict(zip(columns, row)))
            
            conn.close()
            return result
            
        except Exception as e:
            self.logger.error(f"查询执行失败: {str(e)}")
            raise Exception(f"查询执行失败: {str(e)}")
    
    def execute_non_query(self, sql, params=None):
        """
        执行非查询SQL语句（INSERT, UPDATE, DELETE）
        
        Args:
            sql (str): SQL语句
            params (tuple): SQL参数
            
        Returns:
            int: 受影响的行数
        """
        try:
            conn = db_config.get_connection()
            cursor = conn.cursor()
            
            if params:
                cursor.execute(sql, params)
            else:
                cursor.execute(sql)
            
            affected_rows = cursor.rowcount
            conn.commit()
            conn.close()
            
            self.logger.info(f"SQL执行成功，影响行数: {affected_rows}")
            return affected_rows
            
        except Exception as e:
            self.logger.error(f"SQL执行失败: {str(e)}")
            raise Exception(f"SQL执行失败: {str(e)}")
    
    def get_paginated_data(self, table_name, page=1, page_size=10, where_clause="", order_by=""):
        """
        获取分页数据
        
        Args:
            table_name (str): 表名
            page (int): 页码（从1开始）
            page_size (int): 每页记录数
            where_clause (str): WHERE条件子句
            order_by (str): 排序字段
            
        Returns:
            dict: 包含数据和分页信息的字典
        """
        try:
            # 计算偏移量
            offset = (page - 1) * page_size
            
            # 构建查询SQL
            base_sql = f"SELECT * FROM {table_name}"
            if where_clause:
                base_sql += f" WHERE {where_clause}"
            if order_by:
                base_sql += f" ORDER BY {order_by}"
            else:
                # 默认按第一个字段排序
                base_sql += f" ORDER BY (SELECT NULL)"
            
            # 分页查询SQL
            paginated_sql = f"{base_sql} OFFSET {offset} ROWS FETCH NEXT {page_size} ROWS ONLY"
            
            # 获取数据
            data = self.execute_query(paginated_sql)
            
            # 获取总记录数
            count_sql = f"SELECT COUNT(*) as total FROM {table_name}"
            if where_clause:
                count_sql += f" WHERE {where_clause}"
            
            total_result = self.execute_query(count_sql)
            total_records = total_result[0]['total'] if total_result else 0
            
            # 计算总页数
            total_pages = (total_records + page_size - 1) // page_size
            
            return {
                'data': data,
                'current_page': page,
                'page_size': page_size,
                'total_records': total_records,
                'total_pages': total_pages,
                'has_previous': page > 1,
                'has_next': page < total_pages
            }
            
        except Exception as e:
            self.logger.error(f"分页查询失败: {str(e)}")
            raise Exception(f"分页查询失败: {str(e)}")

# 全局数据库助手实例
db_helper = DatabaseHelper()
