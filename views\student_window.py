# -*- coding: utf-8 -*-
"""
学生管理界面
提供学生数据的增删改查界面和分页功能
"""

import tkinter as tk
from tkinter import ttk, messagebox
from models.student import student_model
from utils.validators import validators

class StudentWindow:
    """学生管理窗口类"""
    
    def __init__(self, parent):
        self.window = tk.Toplevel(parent)
        self.window.title("学生管理")
        self.window.geometry("1100x700")
        self.window.resizable(True, True)
        
        # 数据相关属性
        self.current_page = 1
        self.page_size = 10
        self.total_pages = 0
        self.search_term = ""
        
        # 创建界面
        self.create_widgets()
        
        # 加载数据
        self.load_data()
    
    def create_widgets(self):
        """创建界面组件"""
        # 主框架
        main_frame = ttk.Frame(self.window, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置网格权重
        self.window.columnconfigure(0, weight=1)
        self.window.rowconfigure(0, weight=1)
        main_frame.columnconfigure(0, weight=1)
        main_frame.rowconfigure(1, weight=1)
        
        # 创建工具栏
        self.create_toolbar(main_frame)
        
        # 创建数据表格
        self.create_treeview(main_frame)
        
        # 创建分页控件
        self.create_pagination(main_frame)
    
    def create_toolbar(self, parent):
        """创建工具栏"""
        toolbar_frame = ttk.Frame(parent)
        toolbar_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        toolbar_frame.columnconfigure(1, weight=1)
        
        # 搜索框
        ttk.Label(toolbar_frame, text="搜索:").grid(row=0, column=0, padx=(0, 5))
        
        self.search_var = tk.StringVar()
        search_entry = ttk.Entry(toolbar_frame, textvariable=self.search_var, width=30)
        search_entry.grid(row=0, column=1, padx=(0, 10), sticky=tk.W)
        search_entry.bind('<Return>', lambda e: self.search_data())
        
        # 搜索按钮
        search_btn = ttk.Button(toolbar_frame, text="搜索", command=self.search_data)
        search_btn.grid(row=0, column=2, padx=(0, 10))
        
        # 重置按钮
        reset_btn = ttk.Button(toolbar_frame, text="重置", command=self.reset_search)
        reset_btn.grid(row=0, column=3, padx=(0, 20))
        
        # 操作按钮
        add_btn = ttk.Button(toolbar_frame, text="添加学生", command=self.add_student)
        add_btn.grid(row=0, column=4, padx=(0, 5))
        
        edit_btn = ttk.Button(toolbar_frame, text="编辑学生", command=self.edit_student)
        edit_btn.grid(row=0, column=5, padx=(0, 5))
        
        delete_btn = ttk.Button(toolbar_frame, text="删除学生", command=self.delete_student)
        delete_btn.grid(row=0, column=6)
    
    def create_treeview(self, parent):
        """创建数据表格"""
        # 创建框架
        tree_frame = ttk.Frame(parent)
        tree_frame.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        tree_frame.columnconfigure(0, weight=1)
        tree_frame.rowconfigure(0, weight=1)
        
        # 定义列
        columns = ('Sno', 'Sname', 'Sdept', 'Ssex', 'Age', 'Telephone', 'Email')
        
        # 创建Treeview
        self.tree = ttk.Treeview(tree_frame, columns=columns, show='headings', height=15)
        
        # 设置列标题和宽度
        column_configs = {
            'Sno': ('学号', 100),
            'Sname': ('姓名', 100),
            'Sdept': ('系别', 150),
            'Ssex': ('性别', 60),
            'Age': ('年龄', 60),
            'Telephone': ('电话', 120),
            'Email': ('邮箱', 200)
        }
        
        for col, (heading, width) in column_configs.items():
            self.tree.heading(col, text=heading)
            self.tree.column(col, width=width, minwidth=50)
        
        # 创建滚动条
        v_scrollbar = ttk.Scrollbar(tree_frame, orient=tk.VERTICAL, command=self.tree.yview)
        h_scrollbar = ttk.Scrollbar(tree_frame, orient=tk.HORIZONTAL, command=self.tree.xview)
        self.tree.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)
        
        # 布局
        self.tree.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        v_scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        h_scrollbar.grid(row=1, column=0, sticky=(tk.W, tk.E))
        
        # 绑定双击事件
        self.tree.bind('<Double-1>', lambda e: self.edit_student())
    
    def create_pagination(self, parent):
        """创建分页控件"""
        page_frame = ttk.Frame(parent)
        page_frame.grid(row=2, column=0, sticky=(tk.W, tk.E), pady=(10, 0))
        
        # 分页信息标签
        self.page_info_label = ttk.Label(page_frame, text="")
        self.page_info_label.grid(row=0, column=0, padx=(0, 20))
        
        # 分页按钮
        self.prev_btn = ttk.Button(page_frame, text="上一页", command=self.prev_page)
        self.prev_btn.grid(row=0, column=1, padx=(0, 5))
        
        self.next_btn = ttk.Button(page_frame, text="下一页", command=self.next_page)
        self.next_btn.grid(row=0, column=2, padx=(0, 20))
        
        # 跳转页面
        ttk.Label(page_frame, text="跳转到:").grid(row=0, column=3, padx=(0, 5))
        
        self.page_var = tk.StringVar()
        page_entry = ttk.Entry(page_frame, textvariable=self.page_var, width=5)
        page_entry.grid(row=0, column=4, padx=(0, 5))
        page_entry.bind('<Return>', lambda e: self.goto_page())
        
        goto_btn = ttk.Button(page_frame, text="跳转", command=self.goto_page)
        goto_btn.grid(row=0, column=5)
    
    def load_data(self):
        """加载数据"""
        try:
            # 清空现有数据
            for item in self.tree.get_children():
                self.tree.delete(item)
            
            # 获取分页数据
            result = student_model.get_all_paginated(
                page=self.current_page,
                page_size=self.page_size,
                search_term=self.search_term
            )
            
            # 更新分页信息
            self.total_pages = result['total_pages']
            
            # 插入数据到表格
            for student in result['data']:
                self.tree.insert('', tk.END, values=(
                    student.get('Sno', ''),
                    student.get('Sname', ''),
                    student.get('Sdept', ''),
                    student.get('Ssex', ''),
                    student.get('Age', ''),
                    student.get('Telephone', ''),
                    student.get('Email', '')
                ))
            
            # 更新分页控件
            self.update_pagination_controls(result)
            
        except Exception as e:
            messagebox.showerror("错误", f"加载数据失败：{str(e)}")
    
    def update_pagination_controls(self, result):
        """更新分页控件状态"""
        # 更新页面信息
        info_text = f"第 {result['current_page']} 页，共 {result['total_pages']} 页，总计 {result['total_records']} 条记录"
        self.page_info_label.config(text=info_text)
        
        # 更新按钮状态
        self.prev_btn.config(state=tk.NORMAL if result['has_previous'] else tk.DISABLED)
        self.next_btn.config(state=tk.NORMAL if result['has_next'] else tk.DISABLED)
    
    def search_data(self):
        """搜索数据"""
        self.search_term = self.search_var.get().strip()
        self.current_page = 1
        self.load_data()
    
    def reset_search(self):
        """重置搜索"""
        self.search_var.set("")
        self.search_term = ""
        self.current_page = 1
        self.load_data()
    
    def prev_page(self):
        """上一页"""
        if self.current_page > 1:
            self.current_page -= 1
            self.load_data()
    
    def next_page(self):
        """下一页"""
        if self.current_page < self.total_pages:
            self.current_page += 1
            self.load_data()
    
    def goto_page(self):
        """跳转到指定页面"""
        try:
            page = int(self.page_var.get())
            if 1 <= page <= self.total_pages:
                self.current_page = page
                self.load_data()
                self.page_var.set("")
            else:
                messagebox.showwarning("警告", f"页码必须在 1 到 {self.total_pages} 之间")
        except ValueError:
            messagebox.showwarning("警告", "请输入有效的页码")
    
    def add_student(self):
        """添加学生"""
        dialog = StudentDialog(self.window, "添加学生")
        if dialog.result:
            try:
                student_model.create(dialog.result)
                messagebox.showinfo("成功", "学生添加成功")
                self.load_data()
            except Exception as e:
                messagebox.showerror("错误", f"添加学生失败：{str(e)}")
    
    def edit_student(self):
        """编辑学生"""
        selection = self.tree.selection()
        if not selection:
            messagebox.showwarning("警告", "请选择要编辑的学生")
            return
        
        # 获取选中的学生数据
        item = self.tree.item(selection[0])
        sno = item['values'][0]
        
        try:
            student_data = student_model.get_by_id(sno)
            if student_data:
                dialog = StudentDialog(self.window, "编辑学生", student_data)
                if dialog.result:
                    student_model.update(sno, dialog.result)
                    messagebox.showinfo("成功", "学生信息更新成功")
                    self.load_data()
            else:
                messagebox.showerror("错误", "未找到学生信息")
        except Exception as e:
            messagebox.showerror("错误", f"编辑学生失败：{str(e)}")
    
    def delete_student(self):
        """删除学生"""
        selection = self.tree.selection()
        if not selection:
            messagebox.showwarning("警告", "请选择要删除的学生")
            return
        
        # 获取选中的学生编号
        item = self.tree.item(selection[0])
        sno = item['values'][0]
        sname = item['values'][1]
        
        # 确认删除
        if messagebox.askyesno("确认删除", f"确定要删除学生 {sname}（{sno}）吗？\n注意：如果该学生有关联的选课记录，将无法删除。"):
            try:
                student_model.delete(sno)
                messagebox.showinfo("成功", "学生删除成功")
                self.load_data()
            except Exception as e:
                messagebox.showerror("错误", f"删除学生失败：{str(e)}")


class StudentDialog:
    """学生信息对话框"""
    
    def __init__(self, parent, title, student_data=None):
        self.result = None
        
        # 创建对话框窗口
        self.dialog = tk.Toplevel(parent)
        self.dialog.title(title)
        self.dialog.geometry("400x400")
        self.dialog.resizable(False, False)
        self.dialog.transient(parent)
        self.dialog.grab_set()
        
        # 居中显示
        self.center_dialog()
        
        # 创建界面
        self.create_widgets(student_data)
        
        # 等待对话框关闭
        self.dialog.wait_window()
    
    def center_dialog(self):
        """对话框居中显示"""
        self.dialog.update_idletasks()
        width = self.dialog.winfo_width()
        height = self.dialog.winfo_height()
        x = (self.dialog.winfo_screenwidth() // 2) - (width // 2)
        y = (self.dialog.winfo_screenheight() // 2) - (height // 2)
        self.dialog.geometry(f'{width}x{height}+{x}+{y}')
    
    def create_widgets(self, student_data):
        """创建对话框组件"""
        # 主框架
        main_frame = ttk.Frame(self.dialog, padding="20")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 输入字段
        fields = [
            ('Sno', '学号*:', 'entry'),
            ('Spassword', '密码*:', 'entry'),
            ('Sname', '姓名*:', 'entry'),
            ('Sdept', '系别*:', 'entry'),
            ('Ssex', '性别:', 'combobox'),
            ('Age', '年龄:', 'entry'),
            ('Telephone', '电话:', 'entry'),
            ('Email', '邮箱:', 'entry')
        ]
        
        self.vars = {}
        row = 0
        
        for field, label, widget_type in fields:
            ttk.Label(main_frame, text=label).grid(row=row, column=0, sticky=tk.W, pady=5)
            
            if widget_type == 'entry':
                var = tk.StringVar()
                if student_data and field in student_data:
                    var.set(str(student_data[field]) if student_data[field] else '')
                
                entry = ttk.Entry(main_frame, textvariable=var, width=30)
                entry.grid(row=row, column=1, sticky=(tk.W, tk.E), pady=5, padx=(10, 0))
                
                self.vars[field] = var
                
            elif widget_type == 'combobox':
                var = tk.StringVar()
                if student_data and field in student_data:
                    var.set(str(student_data[field]) if student_data[field] else '')
                
                combo = ttk.Combobox(main_frame, textvariable=var, width=28, state='readonly')
                combo['values'] = ('男', '女')
                combo.grid(row=row, column=1, sticky=(tk.W, tk.E), pady=5, padx=(10, 0))
                
                self.vars[field] = var
            
            row += 1
        
        # 按钮框架
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=row, column=0, columnspan=2, pady=(20, 0))
        
        # 确定和取消按钮
        ok_btn = ttk.Button(button_frame, text="确定", command=self.ok_clicked)
        ok_btn.grid(row=0, column=0, padx=(0, 10))
        
        cancel_btn = ttk.Button(button_frame, text="取消", command=self.cancel_clicked)
        cancel_btn.grid(row=0, column=1)
        
        # 配置网格权重
        main_frame.columnconfigure(1, weight=1)
    
    def ok_clicked(self):
        """确定按钮点击事件"""
        # 收集数据
        data = {}
        for field, var in self.vars.items():
            value = var.get().strip()
            if field == 'Age' and value:
                try:
                    data[field] = int(value)
                except ValueError:
                    data[field] = value
            else:
                data[field] = value
        
        # 验证数据
        is_valid, errors = validators.validate_student(data)
        if not is_valid:
            messagebox.showerror("验证错误", "\n".join(errors))
            return
        
        self.result = data
        self.dialog.destroy()
    
    def cancel_clicked(self):
        """取消按钮点击事件"""
        self.dialog.destroy()
