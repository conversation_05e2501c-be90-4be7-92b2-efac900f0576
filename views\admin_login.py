# -*- coding: utf-8 -*-
"""
管理员登录界面
提供管理员身份验证功能
"""

import tkinter as tk
from tkinter import ttk, messagebox
from models.manage import manage_model
from utils.validators import validators

class AdminLoginWindow:
    """管理员登录窗口类"""
    
    def __init__(self):
        self.result = None  # 登录结果，成功时包含管理员信息
        self.login_success = False  # 登录是否成功
        
        # 创建登录窗口
        self.window = tk.Tk()
        self.window.title("管理员登录 - 学生管理系统")
        self.window.geometry("400x300")
        self.window.resizable(False, False)
        
        # 设置窗口图标和属性
        self.window.configure(bg='#f0f0f0')
        
        # 居中显示
        self.center_window()
        
        # 创建界面
        self.create_widgets()
        
        # 设置焦点到用户名输入框
        self.username_entry.focus()
        
        # 绑定回车键登录
        self.window.bind('<Return>', lambda event: self.login())
        
        # 设置窗口关闭事件
        self.window.protocol("WM_DELETE_WINDOW", self.on_closing)
    
    def center_window(self):
        """窗口居中显示"""
        self.window.update_idletasks()
        width = self.window.winfo_width()
        height = self.window.winfo_height()
        x = (self.window.winfo_screenwidth() // 2) - (width // 2)
        y = (self.window.winfo_screenheight() // 2) - (height // 2)
        self.window.geometry(f'{width}x{height}+{x}+{y}')
    
    def create_widgets(self):
        """创建界面组件"""
        # 主框架
        main_frame = ttk.Frame(self.window, padding="30")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置网格权重
        self.window.columnconfigure(0, weight=1)
        self.window.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        
        # 标题
        title_label = ttk.Label(
            main_frame, 
            text="管理员登录", 
            font=("微软雅黑", 18, "bold")
        )
        title_label.grid(row=0, column=0, columnspan=2, pady=(0, 30))
        
        # 用户名标签和输入框
        username_label = ttk.Label(main_frame, text="用户名:", font=("微软雅黑", 11))
        username_label.grid(row=1, column=0, sticky=tk.W, pady=10)
        
        self.username_var = tk.StringVar()
        self.username_entry = ttk.Entry(
            main_frame, 
            textvariable=self.username_var, 
            font=("微软雅黑", 11),
            width=20
        )
        self.username_entry.grid(row=1, column=1, sticky=(tk.W, tk.E), pady=10, padx=(10, 0))
        
        # 密码标签和输入框
        password_label = ttk.Label(main_frame, text="密码:", font=("微软雅黑", 11))
        password_label.grid(row=2, column=0, sticky=tk.W, pady=10)
        
        self.password_var = tk.StringVar()
        self.password_entry = ttk.Entry(
            main_frame, 
            textvariable=self.password_var, 
            font=("微软雅黑", 11),
            width=20,
            show="*"  # 隐藏密码
        )
        self.password_entry.grid(row=2, column=1, sticky=(tk.W, tk.E), pady=10, padx=(10, 0))
        
        # 按钮框架
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=3, column=0, columnspan=2, pady=(30, 0))
        
        # 登录按钮
        login_btn = ttk.Button(
            button_frame, 
            text="登录", 
            command=self.login,
            width=12
        )
        login_btn.grid(row=0, column=0, padx=(0, 10))
        
        # 退出按钮
        exit_btn = ttk.Button(
            button_frame, 
            text="退出", 
            command=self.on_closing,
            width=12
        )
        exit_btn.grid(row=0, column=1, padx=(10, 0))
        
        # 提示信息
        info_label = ttk.Label(
            main_frame, 
            text="默认账号: admin / 123456", 
            font=("微软雅黑", 9),
            foreground="gray"
        )
        info_label.grid(row=4, column=0, columnspan=2, pady=(20, 0))
    
    def login(self):
        """执行登录验证"""
        try:
            # 获取输入数据
            username = self.username_var.get().strip()
            password = self.password_var.get().strip()
            
            # 构建验证数据
            admin_data = {
                'Gno': username,
                'Gpassword': password
            }
            
            # 验证输入格式
            is_valid, errors = validators.validate_admin(admin_data)
            if not is_valid:
                messagebox.showerror("输入错误", "\n".join(errors))
                return
            
            # 验证登录凭据
            admin_info = manage_model.validate_login(username, password)
            if admin_info:
                # 登录成功
                self.result = admin_info
                self.login_success = True
                messagebox.showinfo("登录成功", f"欢迎您，{admin_info['Gname']}！")
                self.window.destroy()
            else:
                # 登录失败
                messagebox.showerror("登录失败", "用户名或密码错误，请重新输入。")
                self.password_var.set("")  # 清空密码
                self.username_entry.focus()
                
        except Exception as e:
            messagebox.showerror("系统错误", f"登录过程中发生错误：{str(e)}")
    
    def on_closing(self):
        """窗口关闭事件处理"""
        if messagebox.askokcancel("确认退出", "确定要退出系统吗？"):
            self.login_success = False
            self.window.destroy()
    
    def run(self):
        """运行登录窗口"""
        self.window.mainloop()
        return self.login_success, self.result
