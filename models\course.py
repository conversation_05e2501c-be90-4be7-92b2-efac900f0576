# -*- coding: utf-8 -*-
"""
课程数据模型
提供课程表的CRUD操作方法
"""

from utils.database_helper import db_helper
import logging

class CourseModel:
    """课程数据模型类"""
    
    def __init__(self):
        self.table_name = "Course"
        self.logger = logging.getLogger(__name__)
    
    def get_all_paginated(self, page=1, page_size=10, search_term=""):
        """
        获取分页的课程数据（包含教师姓名）
        
        Args:
            page (int): 页码
            page_size (int): 每页记录数
            search_term (str): 搜索关键词
            
        Returns:
            dict: 分页数据结果
        """
        try:
            # 计算偏移量
            offset = (page - 1) * page_size
            
            # 构建查询SQL，联接教师表获取教师姓名
            base_sql = """
                SELECT c.Cno, c.Cname, c.Term, c.Tno, t.Tname, c.Ccredit
                FROM Course c
                LEFT JOIN Teacher t ON c.Tno = t.Tno
            """
            
            where_clause = ""
            if search_term:
                where_clause = f"WHERE c.Cname LIKE '%{search_term}%' OR t.Tname LIKE '%{search_term}%' OR c.Cno LIKE '%{search_term}%'"
            
            # 分页查询
            paginated_sql = f"{base_sql} {where_clause} ORDER BY c.Cno OFFSET {offset} ROWS FETCH NEXT {page_size} ROWS ONLY"
            data = db_helper.execute_query(paginated_sql)
            
            # 获取总记录数
            count_sql = f"SELECT COUNT(*) as total FROM Course c LEFT JOIN Teacher t ON c.Tno = t.Tno {where_clause}"
            total_result = db_helper.execute_query(count_sql)
            total_records = total_result[0]['total'] if total_result else 0
            
            # 计算总页数
            total_pages = (total_records + page_size - 1) // page_size
            
            return {
                'data': data,
                'current_page': page,
                'page_size': page_size,
                'total_records': total_records,
                'total_pages': total_pages,
                'has_previous': page > 1,
                'has_next': page < total_pages
            }
            
        except Exception as e:
            self.logger.error(f"获取课程分页数据失败: {str(e)}")
            raise
    
    def get_by_id(self, cno):
        """
        根据课程编号获取课程信息
        
        Args:
            cno (str): 课程编号
            
        Returns:
            dict: 课程信息字典，如果不存在返回None
        """
        try:
            sql = "SELECT * FROM Course WHERE Cno = ?"
            result = db_helper.execute_query(sql, (cno,))
            return result[0] if result else None
        except Exception as e:
            self.logger.error(f"获取课程信息失败: {str(e)}")
            raise
    
    def get_teachers(self):
        """
        获取所有教师列表（用于下拉选择）
        
        Returns:
            list: 教师信息列表
        """
        try:
            sql = "SELECT Tno, Tname FROM Teacher ORDER BY Tno"
            return db_helper.execute_query(sql)
        except Exception as e:
            self.logger.error(f"获取教师列表失败: {str(e)}")
            raise
    
    def create(self, course_data):
        """
        创建新课程记录
        
        Args:
            course_data (dict): 课程信息字典
            
        Returns:
            bool: 创建成功返回True
        """
        try:
            sql = """
                INSERT INTO Course (Cno, Cname, Term, Tno, Ccredit)
                VALUES (?, ?, ?, ?, ?)
            """
            params = (
                course_data['Cno'],
                course_data['Cname'],
                course_data.get('Term', 1),
                course_data.get('Tno', None),
                course_data.get('Ccredit', 0)
            )
            
            affected_rows = db_helper.execute_non_query(sql, params)
            return affected_rows > 0
            
        except Exception as e:
            self.logger.error(f"创建课程记录失败: {str(e)}")
            raise
    
    def update(self, cno, course_data):
        """
        更新课程信息
        
        Args:
            cno (str): 课程编号
            course_data (dict): 更新的课程信息
            
        Returns:
            bool: 更新成功返回True
        """
        try:
            sql = """
                UPDATE Course 
                SET Cname=?, Term=?, Tno=?, Ccredit=?
                WHERE Cno=?
            """
            params = (
                course_data['Cname'],
                course_data.get('Term', 1),
                course_data.get('Tno', None),
                course_data.get('Ccredit', 0),
                cno
            )
            
            affected_rows = db_helper.execute_non_query(sql, params)
            return affected_rows > 0
            
        except Exception as e:
            self.logger.error(f"更新课程信息失败: {str(e)}")
            raise
    
    def delete(self, cno):
        """
        删除课程记录
        
        Args:
            cno (str): 课程编号
            
        Returns:
            bool: 删除成功返回True
        """
        try:
            # 检查是否有关联的选课记录
            check_sql = "SELECT COUNT(*) as count FROM SC WHERE Cno = ?"
            result = db_helper.execute_query(check_sql, (cno,))
            if result[0]['count'] > 0:
                raise Exception("无法删除：该课程还有关联的选课记录")
            
            sql = "DELETE FROM Course WHERE Cno = ?"
            affected_rows = db_helper.execute_non_query(sql, (cno,))
            return affected_rows > 0
            
        except Exception as e:
            self.logger.error(f"删除课程记录失败: {str(e)}")
            raise

# 全局课程模型实例
course_model = CourseModel()
