-- �������ݿ�
CREATE DATABASE StudentManagementSystem;
GO

USE StudentManagementSystem;
GO

-- ������ʦ��
CREATE TABLE Teacher (
    Tno CHAR(5) NOT NULL PRIMARY KEY,
    Tpassword CHAR(6) NOT NULL,
    Tname CHAR(10) NOT NULL,
    Sdept CHAR(20) NOT NULL,
    Duty CHAR(10),
    Telephone CHAR(12),
    Book CHAR(20)
);
GO

-- �����γ̱�
CREATE TABLE Course (
    Cno CHAR(7) NOT NULL PRIMARY KEY,
    Cname CHAR(20) NOT NULL,
    Term INT,
    Tno CHAR(5),
    Ccredit INT,
    FOREIGN KEY (Tno) REFERENCES Teacher(Tno)
);
GO

-- ����ѧ����
CREATE TABLE Student (
    Sno CHAR(12) NOT NULL PRIMARY KEY,
    Spassword CHAR(6) NOT NULL,
    Sname CHAR(10) NOT NULL,
    Sdept CHAR(20) NOT NULL,
    Ssex CHAR(2),
    Age INT,
    Telephone CHAR(12),
    Email VARCHAR(30)
);
GO

-- ����ѡ�α�
CREATE TABLE SC (
    Cno CHAR(7) NOT NULL,
    Sno CHAR(12) NOT NULL,
    Grade INT,
    Cpno CHAR(7),
    PRIMARY KEY (Cno, Sno),
    FOREIGN KEY (Cno) REFERENCES Course(Cno),
    FOREIGN KEY (Sno) REFERENCES Student(Sno)
);
GO

-- ��������Ա��
CREATE TABLE Manage (
    Gno CHAR(12) NOT NULL PRIMARY KEY,
    Gpassword CHAR(6) NOT NULL,
    Gname CHAR(10) NOT NULL,
    Telephone CHAR(12)
);
GO


-- �����ʦ����
INSERT INTO Teacher (Tno, Tpassword, Tname, Sdept, Duty, Telephone, Book)
VALUES 
('T0001', '123456', '����', '�������ѧϵ', '����', '13800138001', '���ݿ�ԭ��'),
('T0002', '123456', '����', '���ӹ���ϵ', '������', '13800138002', '��·����'),
('T0003', '123456', '����', '��е����ϵ', '��ʦ', '13800138003', '��е���'),
('T0004', '123456', '����ϫ', '���̹���ϵ', '����', '13800138004', '����ѧ'),
('T0005', '123456', '��ΰ', '�������ѧϵ', '������', '13800138005', '�㷨����'),
('T0006', '123456', '�Ծ�', '���ӹ���ϵ', '��ʦ', '13800138006', '���ֵ�·'),
('T0007', '123456', '����', '��е����ϵ', '����', '13800138007', '������ѧ'),
('T0008', '123456', '����', '���̹���ϵ', '������', '13800138008', '�г�Ӫ��');
GO

-- ����γ�����
INSERT INTO Course (Cno, Cname, Term, Tno, Ccredit)
VALUES
('CS101', '���ݿ�ϵͳ', 3, 'T0001', 4),
('CS102', '���ݽṹ', 2, 'T0004', 3),
('EE101', '��·ԭ��', 2, 'T0002', 4),
('EE102', '���ֵ�·', 3, 'T0006', 3),
('ME101', '��е���', 3, 'T0003', 4),
('ME102', '������ѧ', 4, 'T0007', 3),
('BA101', '����ѧԭ��', 1, 'T0004', 2),
('BA102', '�г�Ӫ��', 2, 'T0008', 3),
('CS201', '����ϵͳ', 4, 'T0001', 4),
('CS202', '���������', 5, 'T0005', 3);
GO

-- ����ѧ������
INSERT INTO Student (Sno, Spassword, Sname, Sdept, Ssex, Age, Telephone, Email)
VALUES
('202300800651', '123456', '������', '�����', 'Ů', 20, '13806011258', '<EMAIL>'),
('202300000001', '123456', '����', '�������ѧϵ', '��', 20, '13900139001', '<EMAIL>'),
('202300000002', '123456', '����', '�������ѧϵ', '��', 21, '13900139002', '<EMAIL>'),
('202300000003', '123456', '����', '���ӹ���ϵ', '��', 19, '13900139003', '<EMAIL>'),
('202300000004', '123456', '����', '���ӹ���ϵ', '��', 20, '13900139004', '<EMAIL>'),
('202300000005', '123456', 'Ǯ��', '��е����ϵ', 'Ů', 20, '13900139005', '<EMAIL>'),
('202300000006', '123456', '���', '��е����ϵ', 'Ů', 19, '13900139006', '<EMAIL>'),
('202300000007', '123456', '�ܾ�', '���̹���ϵ', '��', 21, '13900139007', '<EMAIL>'),
('202300000008', '123456', '��ʮ', '���̹���ϵ', 'Ů', 20, '13900139008', '<EMAIL>'),
('202300000009', '123456', '֣ʮһ', '�������ѧϵ', '��', 19, '13900139009', '<EMAIL>'),
('202300000010', '123456', '��ʮ��', '���ӹ���ϵ', 'Ů', 20, '13900139010', '<EMAIL>');
GO

-- ����ѡ������
INSERT INTO SC (Cno, Sno, Grade, Cpno)
VALUES
('EE102', '202300800651', 76, 'EE101'),
('EE101', '202300800651', 65, NULL),
('ME101', '202300800651', 90, NULL),
('ME102', '202300800651', 85, 'ME101'),
('CS101', '202300000001', 85, NULL),
('CS102', '202300000001', 78, 'CS101'),
('CS101', '202300000002', 92, NULL),
('EE101', '202300000003', 88, NULL),
('EE102', '202300000003', 76, 'EE101'),
('EE101', '202300000004', 65, NULL),
('ME101', '202300000005', 90, NULL),
('ME102', '202300000005', 85, 'ME101'),
('BA101', '202300000007', 72, NULL),
('BA102', '202300000007', 80, 'BA101'),
('CS201', '202300000001', 95, 'CS102'),
('CS202', '202300000002', 89, 'CS102'),
('EE101', '202300000010', 93, NULL),
('ME101', '202300000006', 70, NULL),
('BA101', '202300000008', 68, NULL);
GO

-- �������Ա����
INSERT INTO Manage (Gno, Gpassword, Gname, Telephone)
VALUES
('admin', '123456', '������', '13806011258');
GO





-- 1. ������ͼ
-- 1.1 ѧ��������Ϣ����ͼ
-- ����ѧ��������Ϣ��ͼ
CREATE VIEW StudentBasicInfo AS
SELECT Sno, Sname, Sdept, Ssex, Age, Telephone, Email
FROM Student;
GO

-- ����ѧ��������Ϣ��ͼ
SELECT * FROM StudentBasicInfo;
GO


-- 1.2 ѧ���ο��������Ϣ����ͼ
-- ����ѧ���ο��������Ϣ��ͼ
CREATE VIEW StudentBookInfo AS
SELECT s.Sno, s.Sname, t.Book AS ReferenceBook
FROM Student s
JOIN Teacher t ON s.Sdept = t.Sdept;
GO

-- ����ѧ���ο��������Ϣ��ͼ
SELECT * FROM StudentBookInfo;
GO

-- 1.3 ��ʦ������Ϣ����ͼ
-- ������ʦ������Ϣ��ͼ
CREATE VIEW TeacherBasicInfo AS
SELECT Tno, Tname, Sdept, Duty, Telephone, Book
FROM Teacher;
GO

-- ���Խ�ʦ������Ϣ��ͼ
SELECT * FROM TeacherBasicInfo;
GO


-- 2. ����������
-- 2.1 ѧ�ż���ɾ���Ĵ�����
-- ����ѧ�ż���ɾ��������
CREATE TRIGGER CascadeDeleteStudent
ON Student
INSTEAD OF DELETE
AS
BEGIN
    -- ��ɾ��ѡ�μ�¼
    DELETE FROM SC
    WHERE Sno IN (SELECT Sno FROM deleted);
    
    -- ��ɾ��ѧ����¼
    DELETE FROM Student
    WHERE Sno IN (SELECT Sno FROM deleted);
END;
GO


-- 2.2 ѧ�������Զ��޸�Ϊ18��Ĵ�����
-- ���������Զ��޸Ĵ�����
CREATE TRIGGER CheckStudentAge
ON Student
AFTER INSERT, UPDATE
AS
BEGIN
    UPDATE Student
    SET Age = 18
    WHERE (Age < 18) AND (Sno IN (SELECT Sno FROM inserted));
END;
GO

-- ���������Զ��޸Ĵ�����
-- ��������С��18��ѧ��
INSERT INTO Student (Sno, Spassword, Sname, Sdept, Ssex, Age)
VALUES ('20230011', '123456', '����ѧ��', '�������ѧϵ', '��', 16);

-- �����
SELECT Sno, Sname, Age FROM Student WHERE Sname = '����ѧ��';
GO

-- 2.3 �û����������ʾ�Ĵ�����
-- �������������ʾ������
CREATE TRIGGER PasswordChangeNotification
ON Student
AFTER UPDATE
AS
BEGIN
    IF UPDATE(Spassword)
    BEGIN
        PRINT '���棺ѧ�������ѱ����ģ�';
        SELECT i.Sno, i.Sname, '�����Ѹ���' AS Notification
        FROM inserted i
        JOIN deleted d ON i.Sno = d.Sno
        WHERE i.Spassword <> d.Spassword;
    END
END;
GO

-- �������������ʾ������
UPDATE Student
SET Spassword = '654321'
WHERE Sno = '202300800651';
GO


-- 3. �����洢����
-- 3.1 ѧ����¼��Ϣ��֤�Ĵ洢����
-- ����ѧ����¼��֤�洢����
CREATE PROCEDURE ValidateStudentLogin
    @StudentID CHAR(10),
    @Password CHAR(6),
    @IsValid BIT OUTPUT
AS
BEGIN
    IF EXISTS (SELECT 1 FROM Student WHERE Sno = @StudentID AND Spassword = @Password)
        SET @IsValid = 1;
    ELSE
        SET @IsValid = 0;
END;
GO

-- ����ѧ����¼��֤�洢����
DECLARE @Result BIT;
EXEC ValidateStudentLogin '202300800651', '123456', @Result OUTPUT;
SELECT @Result AS '��¼��֤���';
GO


-- 3.2 ����ϵ������ѧ����Ϣ�Ĵ洢����
-- ������ϵ������ѧ���洢����
CREATE PROCEDURE GetStudentsByDepartment
    @DeptName CHAR(20)
AS
BEGIN
    SELECT Sno, Sname, Ssex, Age
    FROM Student
    WHERE Sdept = @DeptName
    ORDER BY Sno;
END;
GO

-- ���԰�ϵ������ѧ���洢����
EXEC GetStudentsByDepartment '�������ѧϵ';
GO


-- 3.3 ��ʦ��Ϣ�ǼǵĴ洢����
-- ������ʦ��Ϣ�ǼǴ洢����
CREATE PROCEDURE RegisterTeacher
    @Tno CHAR(5),
    @Tpassword CHAR(6),
    @Tname CHAR(10),
    @Sdept CHAR(20),
    @Duty CHAR(10),
    @Telephone CHAR(12),
    @Book CHAR(20)
AS
BEGIN
    INSERT INTO Teacher (Tno, Tpassword, Tname, Sdept, Duty, Telephone, Book)
    VALUES (@Tno, @Tpassword, @Tname, @Sdept, @Duty, @Telephone, @Book);
END;
GO

-- ���Խ�ʦ��Ϣ�ǼǴ洢����
EXEC RegisterTeacher 'T0009', '123456', '�½�ʦ', '�������ѧϵ', '��ʦ', '13800138009', '���ݿ�ϵͳ';
GO

-- 4. ��������
-- 4.1 ѧ����ϵ����
-- ��ѧ�����ϴ�������
CREATE INDEX IX_Student_Sno ON Student(Sno);
CREATE INDEX IX_Student_Sdept ON Student(Sdept);
-- 4.2 �γ̹�ϵ����
-- �ڿγ̱��ϴ�������
CREATE INDEX IX_Course_Cno ON Course(Cno);
CREATE INDEX IX_Course_Tno ON Course(Tno);
-- 4.3 ��ʦ��ϵ����
-- �ڽ�ʦ���ϴ�������
CREATE INDEX IX_Teacher_Tno ON Teacher(Tno);
CREATE INDEX IX_Teacher_Sdept ON Teacher(Sdept);




-- 5. ���ݿ⹦�ܵ���
-- (һ) ѧ����Ϣ����ģ��
-- 5.1 ��ѯѧ����ȫ����Ϣ
-- ��ѯѧ��"������"��ȫ����Ϣ
SELECT * FROM Student WHERE Sname = '������';


-- ��ѯ"�����"ϵѧ������Ϣ
SELECT * FROM Student WHERE Sdept = '�����';

-- ��ѯ�������ѧ�뼼��ϵѧ������
SELECT COUNT(*) AS StudentCount 
FROM Student 
WHERE Sdept = '�������ѧ�뼼��';


--2. ����ѧ����¼
-- ������ѧ����ʫ��
INSERT INTO Student (Sno, Spassword, Sname, Sdept, Ssex, Age, Telephone, Email)
VALUES ('202300000012', '123456', '��ʫ��', '�����', 'Ů', 20, '13900139012', '<EMAIL>');

-- �����
SELECT * FROM Student WHERE Sname = '��ʫ��';

-- 3. ɾ��ѧ����¼
-- ɾ��ѧ����ʫ��
DELETE FROM Student WHERE Sname = '��ʫ��';

-- �����
SELECT * FROM Student WHERE Sname = '��ʫ��';

-- 4. �޸�ѧ����¼
-- ����ʫ��ת���������ѧ�뼼��ϵ
UPDATE Student
SET Sdept = '�������ѧ�뼼��'
WHERE Sname = '��ʫ��';

-- �����
SELECT Sname, Sdept FROM Student WHERE Sname = '��ʫ��';



-- 1.1 ��ѯ����ͬѧѧϰ�Ŀγ���Ϣ
-- ��ѯ����ͬѧѧϰ�Ŀγ���Ϣ
SELECT c.Cno, c.Cname, sc.Grade
FROM Student s
JOIN SC sc ON s.Sno = sc.Sno
JOIN Course c ON sc.Cno = c.Cno
WHERE s.Sname = '����';

-- 1.2 �г�����ϫ��ʦ�����ڵ����еĿγ���Ϣ
-- �г�����ϫ��ʦ�����ڵĿγ���Ϣ
SELECT c.Cno, c.Cname, c.Term, c.Ccredit
FROM Course c
JOIN Teacher t ON c.Tno = t.Tno
WHERE t.Tname = '����ϫ';

-- 1.3 �г���ʦ"����ϫ"����"���ݽṹ"�γ̵ĳɼ���
-- �г�"���ݽṹ"�γ̳ɼ���
SELECT s.Sno, s.Sname, sc.Grade
FROM Student s
JOIN SC sc ON s.Sno = sc.Sno
JOIN Course c ON sc.Cno = c.Cno
JOIN Teacher t ON c.Tno = t.Tno
WHERE t.Tname = '����ϫ' AND c.Cname = '���ݽṹ'
ORDER BY sc.Grade DESC;

-- 1.4 ����"����ϫ"��ʦ���ڵ�"���ݽṹ"��ƽ����
-- ����"���ݽṹ"�γ�ƽ����
SELECT AVG(sc.Grade) AS AverageGrade
FROM SC sc
JOIN Course c ON sc.Cno = c.Cno
JOIN Teacher t ON c.Tno = t.Tno
WHERE t.Tname = '����ϫ' AND c.Cname = '���ݽṹ';


-- 2. ����γ̼�¼
-- �����¿γ�
INSERT INTO Course (Cno, Cname, Term, Tno, Ccredit)
VALUES ('CS301', '�˹�����', 6, 'T0001', 3);

-- �����
SELECT * FROM Course WHERE Cname = '�˹�����';
-- 3. ɾ���γ̼�¼
-- ɾ���γ�"�˹�����"
DELETE FROM Course WHERE Cname = '�˹�����';

-- �����
SELECT * FROM Course WHERE Cname = '�˹�����';
-- 4. �޸Ŀγ̼�¼
-- �޸�"�ߵ���ѧ"�γ�
UPDATE Course
SET Cname = '���ݽṹ(1)', Ccredit = 6
WHERE Cname = '���ݽṹ';

-- �����
SELECT * FROM Course WHERE Cname LIKE '���ݽṹ%';