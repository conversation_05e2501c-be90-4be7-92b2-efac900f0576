# -*- coding: utf-8 -*-
"""
选课数据模型
提供选课表的CRUD操作方法
"""

from utils.database_helper import db_helper
import logging

def safe_int_convert(value, default=None):
    """
    安全地将值转换为整数

    Args:
        value: 要转换的值
        default: 转换失败时的默认值

    Returns:
        int or None: 转换后的整数值或默认值
    """
    if value is None or value == '':
        return default
    try:
        return int(value)
    except (ValueError, TypeError):
        return default

class SCModel:
    """选课数据模型类"""
    
    def __init__(self):
        self.table_name = "SC"
        self.logger = logging.getLogger(__name__)
    
    def get_all_paginated(self, page=1, page_size=10, search_term=""):
        """
        获取分页的选课数据（包含学生姓名和课程名称）

        Args:
            page (int): 页码
            page_size (int): 每页记录数
            search_term (str): 搜索关键词

        Returns:
            dict: 分页数据结果
        """
        try:
            # 确保page和page_size是整数类型
            page = int(page) if page else 1
            page_size = int(page_size) if page_size else 10
            offset = (page - 1) * page_size

            # 构建查询SQL，联接学生表和课程表获取姓名
            base_sql = """
                SELECT sc.Cno, sc.Sno, s.Sname, c.Cname, sc.Grade, sc.Cpno
                FROM SC sc
                LEFT JOIN Student s ON sc.Sno = s.Sno
                LEFT JOIN Course c ON sc.Cno = c.Cno
            """

            where_clause = ""
            search_params = []
            if search_term:
                where_clause = "WHERE s.Sname LIKE ? OR c.Cname LIKE ? OR sc.Sno LIKE ? OR sc.Cno LIKE ?"
                search_pattern = f"%{search_term}%"
                search_params = [search_pattern, search_pattern, search_pattern, search_pattern]

            # 分页查询 - 使用参数化查询
            paginated_sql = f"{base_sql} {where_clause} ORDER BY sc.Sno, sc.Cno OFFSET ? ROWS FETCH NEXT ? ROWS ONLY"
            params = search_params + [offset, page_size]
            data = db_helper.execute_query(paginated_sql, tuple(params))

            # 获取总记录数
            count_sql = f"SELECT COUNT(*) as total FROM SC sc LEFT JOIN Student s ON sc.Sno = s.Sno LEFT JOIN Course c ON sc.Cno = c.Cno {where_clause}"
            total_result = db_helper.execute_query(count_sql, tuple(search_params) if search_params else None)
            total_records = total_result[0]['total'] if total_result else 0

            # 计算总页数
            total_pages = (total_records + page_size - 1) // page_size

            return {
                'data': data,
                'current_page': page,
                'page_size': page_size,
                'total_records': total_records,
                'total_pages': total_pages,
                'has_previous': page > 1,
                'has_next': page < total_pages
            }
            
        except Exception as e:
            self.logger.error(f"获取选课分页数据失败: {str(e)}")
            raise
    
    def get_by_id(self, cno, sno):
        """
        根据课程编号和学号获取选课信息
        
        Args:
            cno (str): 课程编号
            sno (str): 学号
            
        Returns:
            dict: 选课信息字典，如果不存在返回None
        """
        try:
            sql = "SELECT * FROM SC WHERE Cno = ? AND Sno = ?"
            result = db_helper.execute_query(sql, (cno, sno))
            return result[0] if result else None
        except Exception as e:
            self.logger.error(f"获取选课信息失败: {str(e)}")
            raise
    
    def get_students(self):
        """
        获取所有学生列表（用于下拉选择）
        
        Returns:
            list: 学生信息列表
        """
        try:
            sql = "SELECT Sno, Sname FROM Student ORDER BY Sno"
            return db_helper.execute_query(sql)
        except Exception as e:
            self.logger.error(f"获取学生列表失败: {str(e)}")
            raise
    
    def get_courses(self):
        """
        获取所有课程列表（用于下拉选择）
        
        Returns:
            list: 课程信息列表
        """
        try:
            sql = "SELECT Cno, Cname FROM Course ORDER BY Cno"
            return db_helper.execute_query(sql)
        except Exception as e:
            self.logger.error(f"获取课程列表失败: {str(e)}")
            raise
    
    def create(self, sc_data):
        """
        创建新选课记录
        
        Args:
            sc_data (dict): 选课信息字典
            
        Returns:
            bool: 创建成功返回True
        """
        try:
            sql = """
                INSERT INTO SC (Cno, Sno, Grade, Cpno)
                VALUES (?, ?, ?, ?)
            """
            params = (
                str(sc_data['Cno']),  # 确保课程编号为字符串
                str(sc_data['Sno']),  # 确保学号为字符串
                safe_int_convert(sc_data.get('Grade')),  # 安全转换成绩为整数或None
                str(sc_data.get('Cpno', '')) if sc_data.get('Cpno') else None  # 确保先修课程编号为字符串或None
            )
            
            affected_rows = db_helper.execute_non_query(sql, params)
            return affected_rows > 0
            
        except Exception as e:
            self.logger.error(f"创建选课记录失败: {str(e)}")
            raise
    
    def update(self, cno, sno, sc_data):
        """
        更新选课信息
        
        Args:
            cno (str): 课程编号
            sno (str): 学号
            sc_data (dict): 更新的选课信息
            
        Returns:
            bool: 更新成功返回True
        """
        try:
            sql = """
                UPDATE SC 
                SET Grade=?, Cpno=?
                WHERE Cno=? AND Sno=?
            """
            params = (
                safe_int_convert(sc_data.get('Grade')),  # 安全转换成绩为整数或None
                str(sc_data.get('Cpno', '')) if sc_data.get('Cpno') else None,  # 确保先修课程编号为字符串或None
                str(cno),  # 确保课程编号为字符串
                str(sno)   # 确保学号为字符串
            )
            
            affected_rows = db_helper.execute_non_query(sql, params)
            return affected_rows > 0
            
        except Exception as e:
            self.logger.error(f"更新选课信息失败: {str(e)}")
            raise
    
    def delete(self, cno, sno):
        """
        删除选课记录
        
        Args:
            cno (str): 课程编号
            sno (str): 学号
            
        Returns:
            bool: 删除成功返回True
        """
        try:
            sql = "DELETE FROM SC WHERE Cno = ? AND Sno = ?"
            affected_rows = db_helper.execute_non_query(sql, (str(cno), str(sno)))
            return affected_rows > 0
            
        except Exception as e:
            self.logger.error(f"删除选课记录失败: {str(e)}")
            raise

# 全局选课模型实例
sc_model = SCModel()
