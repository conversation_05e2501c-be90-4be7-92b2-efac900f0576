# -*- coding: utf-8 -*-
"""
选课数据模型
提供选课表的CRUD操作方法
"""

from utils.database_helper import db_helper
import logging

class SCModel:
    """选课数据模型类"""
    
    def __init__(self):
        self.table_name = "SC"
        self.logger = logging.getLogger(__name__)
    
    def get_all_paginated(self, page=1, page_size=10, search_term=""):
        """
        获取分页的选课数据（包含学生姓名和课程名称）
        
        Args:
            page (int): 页码
            page_size (int): 每页记录数
            search_term (str): 搜索关键词
            
        Returns:
            dict: 分页数据结果
        """
        try:
            # 计算偏移量
            offset = (page - 1) * page_size
            
            # 构建查询SQL，联接学生表和课程表获取姓名
            base_sql = """
                SELECT sc.Cno, sc.Sno, s.Sname, c.Cname, sc.Grade, sc.Cpno
                FROM SC sc
                LEFT JOIN Student s ON sc.Sno = s.Sno
                LEFT JOIN Course c ON sc.Cno = c.Cno
            """
            
            where_clause = ""
            if search_term:
                where_clause = f"WHERE s.Sname LIKE '%{search_term}%' OR c.Cname LIKE '%{search_term}%' OR sc.Sno LIKE '%{search_term}%' OR sc.Cno LIKE '%{search_term}%'"
            
            # 分页查询
            paginated_sql = f"{base_sql} {where_clause} ORDER BY sc.Sno, sc.Cno OFFSET {offset} ROWS FETCH NEXT {page_size} ROWS ONLY"
            data = db_helper.execute_query(paginated_sql)
            
            # 获取总记录数
            count_sql = f"SELECT COUNT(*) as total FROM SC sc LEFT JOIN Student s ON sc.Sno = s.Sno LEFT JOIN Course c ON sc.Cno = c.Cno {where_clause}"
            total_result = db_helper.execute_query(count_sql)
            total_records = total_result[0]['total'] if total_result else 0
            
            # 计算总页数
            total_pages = (total_records + page_size - 1) // page_size
            
            return {
                'data': data,
                'current_page': page,
                'page_size': page_size,
                'total_records': total_records,
                'total_pages': total_pages,
                'has_previous': page > 1,
                'has_next': page < total_pages
            }
            
        except Exception as e:
            self.logger.error(f"获取选课分页数据失败: {str(e)}")
            raise
    
    def get_by_id(self, cno, sno):
        """
        根据课程编号和学号获取选课信息
        
        Args:
            cno (str): 课程编号
            sno (str): 学号
            
        Returns:
            dict: 选课信息字典，如果不存在返回None
        """
        try:
            sql = "SELECT * FROM SC WHERE Cno = ? AND Sno = ?"
            result = db_helper.execute_query(sql, (cno, sno))
            return result[0] if result else None
        except Exception as e:
            self.logger.error(f"获取选课信息失败: {str(e)}")
            raise
    
    def get_students(self):
        """
        获取所有学生列表（用于下拉选择）
        
        Returns:
            list: 学生信息列表
        """
        try:
            sql = "SELECT Sno, Sname FROM Student ORDER BY Sno"
            return db_helper.execute_query(sql)
        except Exception as e:
            self.logger.error(f"获取学生列表失败: {str(e)}")
            raise
    
    def get_courses(self):
        """
        获取所有课程列表（用于下拉选择）
        
        Returns:
            list: 课程信息列表
        """
        try:
            sql = "SELECT Cno, Cname FROM Course ORDER BY Cno"
            return db_helper.execute_query(sql)
        except Exception as e:
            self.logger.error(f"获取课程列表失败: {str(e)}")
            raise
    
    def create(self, sc_data):
        """
        创建新选课记录
        
        Args:
            sc_data (dict): 选课信息字典
            
        Returns:
            bool: 创建成功返回True
        """
        try:
            sql = """
                INSERT INTO SC (Cno, Sno, Grade, Cpno)
                VALUES (?, ?, ?, ?)
            """
            params = (
                sc_data['Cno'],
                sc_data['Sno'],
                sc_data.get('Grade', None),
                sc_data.get('Cpno', None)
            )
            
            affected_rows = db_helper.execute_non_query(sql, params)
            return affected_rows > 0
            
        except Exception as e:
            self.logger.error(f"创建选课记录失败: {str(e)}")
            raise
    
    def update(self, cno, sno, sc_data):
        """
        更新选课信息
        
        Args:
            cno (str): 课程编号
            sno (str): 学号
            sc_data (dict): 更新的选课信息
            
        Returns:
            bool: 更新成功返回True
        """
        try:
            sql = """
                UPDATE SC 
                SET Grade=?, Cpno=?
                WHERE Cno=? AND Sno=?
            """
            params = (
                sc_data.get('Grade', None),
                sc_data.get('Cpno', None),
                cno,
                sno
            )
            
            affected_rows = db_helper.execute_non_query(sql, params)
            return affected_rows > 0
            
        except Exception as e:
            self.logger.error(f"更新选课信息失败: {str(e)}")
            raise
    
    def delete(self, cno, sno):
        """
        删除选课记录
        
        Args:
            cno (str): 课程编号
            sno (str): 学号
            
        Returns:
            bool: 删除成功返回True
        """
        try:
            sql = "DELETE FROM SC WHERE Cno = ? AND Sno = ?"
            affected_rows = db_helper.execute_non_query(sql, (cno, sno))
            return affected_rows > 0
            
        except Exception as e:
            self.logger.error(f"删除选课记录失败: {str(e)}")
            raise

# 全局选课模型实例
sc_model = SCModel()
