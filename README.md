# 学生管理系统

基于Python Tkinter + SQL Server的数据库管理系统，提供完整的学生、教师、课程和选课信息管理功能。

## 系统特性

- **用户友好的图形界面**：基于Tkinter开发，界面简洁直观
- **完整的CRUD操作**：支持数据的增加、查询、修改、删除
- **分页显示**：每页显示10条记录，支持页面跳转
- **搜索功能**：支持关键词搜索和筛选
- **数据验证**：完善的输入数据格式验证
- **关联约束检查**：删除时自动检查外键约束关系

## 功能模块

### 1. 教师管理
- 教师基本信息维护（编号、姓名、系别、职务、电话、教材）
- 支持按姓名、系别搜索
- 删除时检查是否有关联课程

### 2. 学生管理
- 学生基本信息维护（学号、姓名、系别、性别、年龄、电话、邮箱）
- 支持按姓名、学号、系别搜索
- 删除时检查是否有关联选课记录

### 3. 课程管理
- 课程信息维护（课程编号、名称、学期、任课教师、学分）
- 显示教师姓名，支持下拉选择任课教师
- 支持按课程名称、教师姓名搜索
- 删除时检查是否有关联选课记录

### 4. 选课管理
- 选课记录维护（课程、学生、成绩、先修课程）
- 显示课程名称和学生姓名
- 支持下拉选择课程和学生
- 支持按学生姓名、课程名称搜索

## 技术架构

```
学生管理系统/
├── main.py                 # 主程序入口
├── requirements.txt        # 依赖包列表
├── config/                 # 配置模块
│   ├── __init__.py
│   └── database.py         # 数据库配置
├── models/                 # 数据模型层
│   ├── __init__.py
│   ├── teacher.py          # 教师模型
│   ├── student.py          # 学生模型
│   ├── course.py           # 课程模型
│   └── sc.py              # 选课模型
├── views/                  # 视图层
│   ├── __init__.py
│   ├── main_window.py      # 主窗口
│   ├── teacher_window.py   # 教师管理窗口
│   ├── student_window.py   # 学生管理窗口
│   ├── course_window.py    # 课程管理窗口
│   └── sc_window.py        # 选课管理窗口
└── utils/                  # 工具模块
    ├── __init__.py
    ├── database_helper.py  # 数据库助手
    └── validators.py       # 数据验证器
```

## 安装和配置

### 1. 环境要求
- Python 3.7+
- SQL Server 2016+
- ODBC Driver 17 for SQL Server

### 2. 安装依赖
```bash
pip install -r requirements.txt
```

### 3. 数据库配置
1. 确保SQL Server服务正在运行
2. 执行`database.sql`脚本创建数据库和表
3. 修改`config/database.py`中的数据库连接配置：
   ```python
   self.server = 'localhost'  # 数据库服务器地址
   self.database = 'StudentManagementSystem'  # 数据库名称
   self.username = 'sa'  # 用户名
   self.password = 'your_password'  # 密码
   ```

### 4. 运行系统
```bash
python main.py
```

## 使用说明

### 启动系统
1. 运行`python main.py`
2. 系统会自动测试数据库连接
3. 连接成功后显示主界面

### 基本操作
1. **查看数据**：点击功能菜单进入相应管理模块
2. **搜索数据**：在搜索框输入关键词，点击搜索按钮
3. **添加数据**：点击"添加"按钮，填写表单信息
4. **编辑数据**：双击表格行或点击"编辑"按钮
5. **删除数据**：选中记录后点击"删除"按钮
6. **分页浏览**：使用"上一页"、"下一页"或直接跳转

### 注意事项
- 带*号的字段为必填项
- 删除操作会检查关联约束，有关联数据时无法删除
- 电话号码格式：11位手机号
- 邮箱格式：标准邮箱格式
- 数字字段：年龄、学期、学分、成绩必须为有效数字

## 数据库表结构

### Teacher（教师表）
- Tno：教师编号（主键，CHAR(5)）
- Tpassword：密码（CHAR(6)）
- Tname：姓名（CHAR(10)）
- Sdept：系别（CHAR(20)）
- Duty：职务（CHAR(10)）
- Telephone：电话（CHAR(12)）
- Book：教材（CHAR(20)）

### Student（学生表）
- Sno：学号（主键，CHAR(10)）
- Spassword：密码（CHAR(6)）
- Sname：姓名（CHAR(10)）
- Sdept：系别（CHAR(20)）
- Ssex：性别（CHAR(2)）
- Age：年龄（INT）
- Telephone：电话（CHAR(12)）
- Email：邮箱（VARCHAR(30)）

### Course（课程表）
- Cno：课程编号（主键，CHAR(7)）
- Cname：课程名称（CHAR(20)）
- Term：学期（INT）
- Tno：教师编号（外键，CHAR(5)）
- Ccredit：学分（INT）

### SC（选课表）
- Cno：课程编号（复合主键，CHAR(7)）
- Sno：学号（复合主键，CHAR(10)）
- Grade：成绩（INT）
- Cpno：先修课程（CHAR(7)）

## 开发说明

### 代码结构
- **MVC架构**：模型-视图-控制器分离
- **模块化设计**：功能模块独立，便于维护
- **统一异常处理**：完善的错误提示和日志记录
- **数据验证**：前端验证+后端约束双重保障

### 扩展功能
系统采用模块化设计，可以方便地扩展新功能：
1. 在`models/`目录添加新的数据模型
2. 在`views/`目录添加新的界面窗口
3. 在`utils/`目录添加新的工具函数
4. 在主窗口菜单中添加新的功能入口

## 技术支持

如有问题或建议，请检查：
1. 数据库连接配置是否正确
2. SQL Server服务是否正常运行
3. ODBC驱动是否正确安装
4. Python依赖包是否完整安装

## 版本信息

- 版本：1.0.0
- 开发语言：Python 3.7+
- GUI框架：Tkinter
- 数据库：SQL Server
- 开发时间：2025年6月
