# -*- coding: utf-8 -*-
"""
数据验证工具
提供输入数据格式验证功能
"""

import re

class Validators:
    """数据验证器类"""
    
    @staticmethod
    def validate_required(value, field_name):
        """
        验证必填字段
        
        Args:
            value (str): 待验证的值
            field_name (str): 字段名称
            
        Returns:
            tuple: (是否有效, 错误信息)
        """
        if not value or str(value).strip() == "":
            return False, f"{field_name}不能为空"
        return True, ""
    
    @staticmethod
    def validate_length(value, max_length, field_name):
        """
        验证字段长度
        
        Args:
            value (str): 待验证的值
            max_length (int): 最大长度
            field_name (str): 字段名称
            
        Returns:
            tuple: (是否有效, 错误信息)
        """
        if len(str(value)) > max_length:
            return False, f"{field_name}长度不能超过{max_length}个字符"
        return True, ""
    
    @staticmethod
    def validate_number(value, field_name):
        """
        验证数字字段
        
        Args:
            value (str): 待验证的值
            field_name (str): 字段名称
            
        Returns:
            tuple: (是否有效, 错误信息)
        """
        try:
            int(value)
            return True, ""
        except ValueError:
            return False, f"{field_name}必须是有效的数字"
    
    @staticmethod
    def validate_email(email):
        """
        验证邮箱格式
        
        Args:
            email (str): 邮箱地址
            
        Returns:
            tuple: (是否有效, 错误信息)
        """
        if not email:
            return True, ""  # 邮箱可以为空
        
        pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        if re.match(pattern, email):
            return True, ""
        else:
            return False, "邮箱格式不正确"
    
    @staticmethod
    def validate_phone(phone):
        """
        验证电话号码格式
        
        Args:
            phone (str): 电话号码
            
        Returns:
            tuple: (是否有效, 错误信息)
        """
        if not phone:
            return True, ""  # 电话可以为空
        
        pattern = r'^1[3-9]\d{9}$'
        if re.match(pattern, phone):
            return True, ""
        else:
            return False, "电话号码格式不正确（应为11位手机号）"
    
    @staticmethod
    def validate_teacher(teacher_data):
        """
        验证教师数据
        
        Args:
            teacher_data (dict): 教师数据字典
            
        Returns:
            tuple: (是否有效, 错误信息列表)
        """
        errors = []
        
        # 验证必填字段
        required_fields = [
            ('Tno', '教师编号'),
            ('Tpassword', '密码'),
            ('Tname', '姓名'),
            ('Sdept', '系别')
        ]
        
        for field, name in required_fields:
            is_valid, error = Validators.validate_required(teacher_data.get(field), name)
            if not is_valid:
                errors.append(error)
        
        # 验证字段长度
        length_fields = [
            ('Tno', 5, '教师编号'),
            ('Tpassword', 6, '密码'),
            ('Tname', 10, '姓名'),
            ('Sdept', 20, '系别'),
            ('Duty', 10, '职务'),
            ('Telephone', 12, '电话'),
            ('Book', 20, '教材')
        ]
        
        for field, max_len, name in length_fields:
            value = teacher_data.get(field, '')
            if value:
                is_valid, error = Validators.validate_length(value, max_len, name)
                if not is_valid:
                    errors.append(error)
        
        # 验证电话号码
        phone = teacher_data.get('Telephone', '')
        if phone:
            is_valid, error = Validators.validate_phone(phone)
            if not is_valid:
                errors.append(error)
        
        return len(errors) == 0, errors
    
    @staticmethod
    def validate_student(student_data):
        """
        验证学生数据
        
        Args:
            student_data (dict): 学生数据字典
            
        Returns:
            tuple: (是否有效, 错误信息列表)
        """
        errors = []
        
        # 验证必填字段
        required_fields = [
            ('Sno', '学号'),
            ('Spassword', '密码'),
            ('Sname', '姓名'),
            ('Sdept', '系别')
        ]
        
        for field, name in required_fields:
            is_valid, error = Validators.validate_required(student_data.get(field), name)
            if not is_valid:
                errors.append(error)
        
        # 验证字段长度
        length_fields = [
            ('Sno', 10, '学号'),
            ('Spassword', 6, '密码'),
            ('Sname', 10, '姓名'),
            ('Sdept', 20, '系别'),
            ('Ssex', 2, '性别'),
            ('Telephone', 12, '电话')
        ]
        
        for field, max_len, name in length_fields:
            value = student_data.get(field, '')
            if value:
                is_valid, error = Validators.validate_length(value, max_len, name)
                if not is_valid:
                    errors.append(error)
        
        # 验证年龄
        age = student_data.get('Age', '')
        if age:
            is_valid, error = Validators.validate_number(age, '年龄')
            if not is_valid:
                errors.append(error)
        
        # 验证电话号码
        phone = student_data.get('Telephone', '')
        if phone:
            is_valid, error = Validators.validate_phone(phone)
            if not is_valid:
                errors.append(error)
        
        # 验证邮箱
        email = student_data.get('Email', '')
        if email:
            is_valid, error = Validators.validate_email(email)
            if not is_valid:
                errors.append(error)
        
        return len(errors) == 0, errors
    
    @staticmethod
    def validate_course(course_data):
        """
        验证课程数据
        
        Args:
            course_data (dict): 课程数据字典
            
        Returns:
            tuple: (是否有效, 错误信息列表)
        """
        errors = []
        
        # 验证必填字段
        required_fields = [
            ('Cno', '课程编号'),
            ('Cname', '课程名称')
        ]
        
        for field, name in required_fields:
            is_valid, error = Validators.validate_required(course_data.get(field), name)
            if not is_valid:
                errors.append(error)
        
        # 验证字段长度
        length_fields = [
            ('Cno', 7, '课程编号'),
            ('Cname', 20, '课程名称'),
            ('Tno', 5, '教师编号')
        ]
        
        for field, max_len, name in length_fields:
            value = course_data.get(field, '')
            if value:
                is_valid, error = Validators.validate_length(value, max_len, name)
                if not is_valid:
                    errors.append(error)
        
        # 验证学期和学分
        for field, name in [('Term', '学期'), ('Ccredit', '学分')]:
            value = course_data.get(field, '')
            if value:
                is_valid, error = Validators.validate_number(value, name)
                if not is_valid:
                    errors.append(error)
        
        return len(errors) == 0, errors

# 全局验证器实例
validators = Validators()
