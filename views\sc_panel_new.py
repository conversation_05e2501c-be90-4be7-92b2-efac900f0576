# -*- coding: utf-8 -*-
"""
教师管理面板
提供教师数据的增删改查界面和分页功能（内嵌式）
"""

from views.base_panel import BasePanel
from models.teacher import teacher_model

class TeacherPanel(BasePanel):
    """教师管理面板类"""

    def get_columns(self):
        """返回表格列定义"""
        return ('Tno', 'Tname', 'Sdept', 'Duty', 'Telephone', 'Book')

    def get_column_configs(self):
        """返回列配置字典"""
        return {
            'Tno': ('教师编号', 80),
            'Tname': ('姓名', 100),
            'Sdept': ('系别', 150),
            'Duty': ('职务', 100),
            'Telephone': ('电话', 120),
            'Book': ('教材', 150)
        }

    def get_model(self):
        """返回数据模型对象"""
        return teacher_model

    def get_entity_name(self):
        """返回实体名称"""
        return "教师"

    def get_dialog_class(self):
        """返回对话框类"""
        from views.teacher_window import TeacherDialog
        return TeacherDialog

    def extract_row_data(self, data_item):
        """从数据项中提取行显示数据"""
        return (
            data_item.get('Tno', ''),
            data_item.get('Tname', ''),
            data_item.get('Sdept', ''),
            data_item.get('Duty', ''),
            data_item.get('Telephone', ''),
            data_item.get('Book', '')
        )

    def get_primary_key(self, tree_item_values):
        """从表格行数据中提取主键"""
        return tree_item_values[0]  # Tno
