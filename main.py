# -*- coding: utf-8 -*-
"""
学生管理系统主程序
提供系统启动和数据库连接测试功能
"""

import tkinter as tk
from tkinter import messagebox
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from views.main_window import MainWindow
from config.database import db_config

def test_database_connection():
    """测试数据库连接"""
    try:
        if db_config.test_connection():
            return True
        else:
            return False
    except Exception as e:
        messagebox.showerror(
            "数据库连接错误", 
            f"无法连接到数据库，请检查以下配置：\n\n"
            f"服务器地址：{db_config.server}\n"
            f"数据库名称：{db_config.database}\n"
            f"用户名：{db_config.username}\n\n"
            f"错误信息：{str(e)}\n\n"
            f"请在 config/database.py 文件中修改数据库连接配置。"
        )
        return False

    
    messagebox.showinfo("学生管理系统", info_message)

def main():
    """主函数"""
    # 创建根窗口（隐藏）
    root = tk.Tk()
    root.withdraw()  # 隐藏主窗口
    
    
    # 测试数据库连接
    if not test_database_connection():
        messagebox.showerror("启动失败", "数据库连接失败，程序将退出。")
        return
    # 销毁根窗口
    root.destroy()
    
    # 启动主应用程序
    try:
        app = MainWindow()
        app.run()
    except Exception as e:
        messagebox.showerror("系统错误", f"系统启动失败：{str(e)}")

if __name__ == "__main__":
    main()
