# -*- coding: utf-8 -*-
"""
数据库配置文件
提供SQL Server数据库连接配置和连接管理功能
"""

import pyodbc
import logging

class DatabaseConfig:
    """数据库配置类"""
    
    def __init__(self):
        # SQL Server连接配置
        self.server = 'localhost'  # 数据库服务器地址
        self.database = 'StudentManagementSystem'  # 数据库名称
        self.username = 'sa'  # 用户名
        self.password = '123456'  # 密码，请根据实际情况修改
        self.driver = '{ODBC Driver 17 for SQL Server}'  # ODBC驱动
        
        # 连接字符串
        self.connection_string = (
            f'DRIVER={self.driver};'
            f'SERVER={self.server};'
            f'DATABASE={self.database};'
            f'UID={self.username};'
            f'PWD={self.password};'
            'Trusted_Connection=no;'
        )
        
        # 配置日志
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)
    
    def get_connection(self):
        """
        获取数据库连接
        
        Returns:
            pyodbc.Connection: 数据库连接对象
            
        Raises:
            Exception: 数据库连接失败时抛出异常
        """
        try:
            connection = pyodbc.connect(self.connection_string)
            self.logger.info("数据库连接成功")
            return connection
        except Exception as e:
            self.logger.error(f"数据库连接失败: {str(e)}")
            raise Exception(f"数据库连接失败: {str(e)}")
    
    def test_connection(self):
        """
        测试数据库连接
        
        Returns:
            bool: 连接成功返回True，失败返回False
        """
        try:
            conn = self.get_connection()
            conn.close()
            return True
        except Exception:
            return False

# 全局数据库配置实例
db_config = DatabaseConfig()
