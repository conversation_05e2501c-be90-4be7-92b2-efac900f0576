# -*- coding: utf-8 -*-
"""
学生数据模型
提供学生表的CRUD操作方法
"""

from utils.database_helper import db_helper
import logging

class StudentModel:
    """学生数据模型类"""
    
    def __init__(self):
        self.table_name = "Student"
        self.logger = logging.getLogger(__name__)
    
    def get_all_paginated(self, page=1, page_size=10, search_term=""):
        """
        获取分页的学生数据
        
        Args:
            page (int): 页码
            page_size (int): 每页记录数
            search_term (str): 搜索关键词
            
        Returns:
            dict: 分页数据结果
        """
        try:
            where_clause = ""
            if search_term:
                where_clause = f"Sname LIKE '%{search_term}%' OR Sdept LIKE '%{search_term}%' OR Sno LIKE '%{search_term}%'"
            
            return db_helper.get_paginated_data(
                self.table_name, 
                page, 
                page_size, 
                where_clause, 
                "Sno"
            )
        except Exception as e:
            self.logger.error(f"获取学生分页数据失败: {str(e)}")
            raise
    
    def get_by_id(self, sno):
        """
        根据学号获取学生信息
        
        Args:
            sno (str): 学号
            
        Returns:
            dict: 学生信息字典，如果不存在返回None
        """
        try:
            sql = "SELECT * FROM Student WHERE Sno = ?"
            result = db_helper.execute_query(sql, (sno,))
            return result[0] if result else None
        except Exception as e:
            self.logger.error(f"获取学生信息失败: {str(e)}")
            raise
    
    def create(self, student_data):
        """
        创建新学生记录
        
        Args:
            student_data (dict): 学生信息字典
            
        Returns:
            bool: 创建成功返回True
        """
        try:
            sql = """
                INSERT INTO Student (Sno, Spassword, Sname, Sdept, Ssex, Age, Telephone, Email)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            """
            params = (
                student_data['Sno'],
                student_data['Spassword'],
                student_data['Sname'],
                student_data['Sdept'],
                student_data.get('Ssex', ''),
                student_data.get('Age', 0),
                student_data.get('Telephone', ''),
                student_data.get('Email', '')
            )
            
            affected_rows = db_helper.execute_non_query(sql, params)
            return affected_rows > 0
            
        except Exception as e:
            self.logger.error(f"创建学生记录失败: {str(e)}")
            raise
    
    def update(self, sno, student_data):
        """
        更新学生信息
        
        Args:
            sno (str): 学号
            student_data (dict): 更新的学生信息
            
        Returns:
            bool: 更新成功返回True
        """
        try:
            sql = """
                UPDATE Student 
                SET Spassword=?, Sname=?, Sdept=?, Ssex=?, Age=?, Telephone=?, Email=?
                WHERE Sno=?
            """
            params = (
                student_data['Spassword'],
                student_data['Sname'],
                student_data['Sdept'],
                student_data.get('Ssex', ''),
                student_data.get('Age', 0),
                student_data.get('Telephone', ''),
                student_data.get('Email', ''),
                sno
            )
            
            affected_rows = db_helper.execute_non_query(sql, params)
            return affected_rows > 0
            
        except Exception as e:
            self.logger.error(f"更新学生信息失败: {str(e)}")
            raise
    
    def delete(self, sno):
        """
        删除学生记录
        
        Args:
            sno (str): 学号
            
        Returns:
            bool: 删除成功返回True
        """
        try:
            # 检查是否有关联的选课记录
            check_sql = "SELECT COUNT(*) as count FROM SC WHERE Sno = ?"
            result = db_helper.execute_query(check_sql, (sno,))
            if result[0]['count'] > 0:
                raise Exception("无法删除：该学生还有关联的选课记录")
            
            sql = "DELETE FROM Student WHERE Sno = ?"
            affected_rows = db_helper.execute_non_query(sql, (sno,))
            return affected_rows > 0
            
        except Exception as e:
            self.logger.error(f"删除学生记录失败: {str(e)}")
            raise

# 全局学生模型实例
student_model = StudentModel()
