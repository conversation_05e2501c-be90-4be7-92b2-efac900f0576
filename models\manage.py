# -*- coding: utf-8 -*-
"""
管理员数据模型
提供管理员表的CRUD操作方法
"""

from utils.database_helper import db_helper
import logging

class ManageModel:
    """管理员数据模型类"""
    
    def __init__(self):
        self.table_name = "Manage"
        self.logger = logging.getLogger(__name__)
    
    def get_all_paginated(self, page=1, page_size=10, search_term=""):
        """
        获取分页的管理员数据
        
        Args:
            page (int): 页码
            page_size (int): 每页记录数
            search_term (str): 搜索关键词
            
        Returns:
            dict: 分页数据结果
        """
        try:
            where_clause = ""
            if search_term:
                where_clause = f"Gname LIKE '%{search_term}%' OR Gno LIKE '%{search_term}%'"
            
            return db_helper.get_paginated_data(
                self.table_name, 
                page, 
                page_size, 
                where_clause, 
                "Gno"
            )
        except Exception as e:
            self.logger.error(f"获取管理员分页数据失败: {str(e)}")
            raise
    
    def get_by_id(self, gno):
        """
        根据管理员编号获取管理员信息
        
        Args:
            gno (str): 管理员编号
            
        Returns:
            dict: 管理员信息字典，如果不存在返回None
        """
        try:
            sql = "SELECT * FROM Manage WHERE Gno = ?"
            result = db_helper.execute_query(sql, (gno,))
            return result[0] if result else None
        except Exception as e:
            self.logger.error(f"获取管理员信息失败: {str(e)}")
            raise
    
    def validate_login(self, gno, password):
        """
        验证管理员登录
        
        Args:
            gno (str): 管理员编号
            password (str): 密码
            
        Returns:
            dict: 管理员信息字典，如果验证失败返回None
        """
        try:
            sql = "SELECT * FROM Manage WHERE Gno = ? AND Gpassword = ?"
            result = db_helper.execute_query(sql, (gno, password))
            return result[0] if result else None
        except Exception as e:
            self.logger.error(f"管理员登录验证失败: {str(e)}")
            raise
    
    def create(self, manage_data):
        """
        创建新管理员记录
        
        Args:
            manage_data (dict): 管理员信息字典
            
        Returns:
            bool: 创建成功返回True
        """
        try:
            sql = """
                INSERT INTO Manage (Gno, Gpassword, Gname, Telephone)
                VALUES (?, ?, ?, ?)
            """
            params = (
                manage_data['Gno'],
                manage_data['Gpassword'],
                manage_data['Gname'],
                manage_data.get('Telephone', '')
            )
            
            affected_rows = db_helper.execute_non_query(sql, params)
            return affected_rows > 0
            
        except Exception as e:
            self.logger.error(f"创建管理员记录失败: {str(e)}")
            raise
    
    def update(self, gno, manage_data):
        """
        更新管理员信息
        
        Args:
            gno (str): 管理员编号
            manage_data (dict): 更新的管理员信息
            
        Returns:
            bool: 更新成功返回True
        """
        try:
            sql = """
                UPDATE Manage 
                SET Gpassword=?, Gname=?, Telephone=?
                WHERE Gno=?
            """
            params = (
                manage_data['Gpassword'],
                manage_data['Gname'],
                manage_data.get('Telephone', ''),
                gno
            )
            
            affected_rows = db_helper.execute_non_query(sql, params)
            return affected_rows > 0
            
        except Exception as e:
            self.logger.error(f"更新管理员信息失败: {str(e)}")
            raise
    
    def delete(self, gno):
        """
        删除管理员记录
        
        Args:
            gno (str): 管理员编号
            
        Returns:
            bool: 删除成功返回True
        """
        try:
            sql = "DELETE FROM Manage WHERE Gno = ?"
            affected_rows = db_helper.execute_non_query(sql, (gno,))
            return affected_rows > 0
            
        except Exception as e:
            self.logger.error(f"删除管理员记录失败: {str(e)}")
            raise
    
    def change_password(self, gno, old_password, new_password):
        """
        修改管理员密码
        
        Args:
            gno (str): 管理员编号
            old_password (str): 旧密码
            new_password (str): 新密码
            
        Returns:
            bool: 修改成功返回True
        """
        try:
            # 验证旧密码
            if not self.validate_login(gno, old_password):
                raise Exception("旧密码不正确")
            
            sql = "UPDATE Manage SET Gpassword = ? WHERE Gno = ?"
            affected_rows = db_helper.execute_non_query(sql, (new_password, gno))
            return affected_rows > 0
            
        except Exception as e:
            self.logger.error(f"修改管理员密码失败: {str(e)}")
            raise

# 全局管理员模型实例
manage_model = ManageModel()
