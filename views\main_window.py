# -*- coding: utf-8 -*-
"""
主窗口界面
提供系统主界面和菜单导航功能
"""

import tkinter as tk
from tkinter import ttk, messagebox
from views.teacher_window import TeacherWindow
from views.student_window import StudentWindow
from views.course_window import CourseWindow
from views.sc_window import SCWindow

class MainWindow:
    """主窗口类"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("学生管理系统 - 数据库管理")
        self.root.geometry("1200x800")
        self.root.resizable(True, True)
        
        # 设置窗口居中
        self.center_window()
        
        # 创建界面
        self.create_widgets()
        
        # 子窗口引用
        self.teacher_window = None
        self.student_window = None
        self.course_window = None
        self.sc_window = None
    
    def center_window(self):
        """窗口居中显示"""
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f'{width}x{height}+{x}+{y}')
    
    def create_widgets(self):
        """创建界面组件"""
        # 创建主框架
        main_frame = ttk.Frame(self.root, padding="20")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(1, weight=1)
        
        # 标题
        title_label = ttk.Label(
            main_frame, 
            text="学生管理系统", 
            font=("微软雅黑", 24, "bold")
        )
        title_label.grid(row=0, column=0, columnspan=2, pady=(0, 30))
        
        # 左侧菜单框架
        menu_frame = ttk.LabelFrame(main_frame, text="功能菜单", padding="15")
        menu_frame.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(0, 20))
        
        # 右侧信息框架
        info_frame = ttk.LabelFrame(main_frame, text="系统信息", padding="15")
        info_frame.grid(row=1, column=1, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 创建菜单按钮
        self.create_menu_buttons(menu_frame)
        
        # 创建信息显示
        self.create_info_display(info_frame)
    
    def create_menu_buttons(self, parent):
        """创建菜单按钮"""
        # 按钮样式配置
        button_style = {
            'width': 20,
            'padding': (10, 8)
        }
        
        # 教师管理按钮
        teacher_btn = ttk.Button(
            parent,
            text="教师管理",
            command=self.open_teacher_window,
            **button_style
        )
        teacher_btn.grid(row=0, column=0, pady=10, sticky=tk.W+tk.E)
        
        # 学生管理按钮
        student_btn = ttk.Button(
            parent,
            text="学生管理",
            command=self.open_student_window,
            **button_style
        )
        student_btn.grid(row=1, column=0, pady=10, sticky=tk.W+tk.E)
        
        # 课程管理按钮
        course_btn = ttk.Button(
            parent,
            text="课程管理",
            command=self.open_course_window,
            **button_style
        )
        course_btn.grid(row=2, column=0, pady=10, sticky=tk.W+tk.E)
        
        # 选课管理按钮
        sc_btn = ttk.Button(
            parent,
            text="选课管理",
            command=self.open_sc_window,
            **button_style
        )
        sc_btn.grid(row=3, column=0, pady=10, sticky=tk.W+tk.E)
        
        # 分隔线
        separator = ttk.Separator(parent, orient='horizontal')
        separator.grid(row=4, column=0, sticky=tk.W+tk.E, pady=20)
        
        # 退出按钮
        exit_btn = ttk.Button(
            parent,
            text="退出系统",
            command=self.exit_application,
            **button_style
        )
        exit_btn.grid(row=5, column=0, pady=10, sticky=tk.W+tk.E)
        
        # 配置列权重
        parent.columnconfigure(0, weight=1)
    
    def create_info_display(self, parent):
        """创建信息显示区域"""
        # 系统介绍
        intro_text = """
        欢迎使用学生管理系统！
        
        本系统提供以下功能：
        
        • 教师管理：管理教师基本信息
        • 学生管理：管理学生基本信息
        • 课程管理：管理课程信息和教师分配
        • 选课管理：管理学生选课和成绩信息
        
        每个模块都支持：
        - 数据查看（分页显示，每页10条记录）
        - 添加新记录
        - 编辑现有记录
        - 删除记录
        - 搜索功能
        
        使用说明：
        1. 点击左侧菜单按钮进入相应管理模块
        2. 在数据表格中双击记录可进行编辑
        3. 使用搜索框可快速查找数据
        4. 注意数据间的关联关系，删除时会检查约束
        """
        
        # 创建文本显示区域
        text_widget = tk.Text(
            parent,
            wrap=tk.WORD,
            font=("微软雅黑", 11),
            bg="#f8f9fa",
            relief=tk.FLAT,
            padx=15,
            pady=15
        )
        text_widget.insert(tk.END, intro_text)
        text_widget.config(state=tk.DISABLED)  # 设置为只读
        
        # 创建滚动条
        scrollbar = ttk.Scrollbar(parent, orient=tk.VERTICAL, command=text_widget.yview)
        text_widget.configure(yscrollcommand=scrollbar.set)
        
        # 布局
        text_widget.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        
        # 配置网格权重
        parent.columnconfigure(0, weight=1)
        parent.rowconfigure(0, weight=1)
    
    def open_teacher_window(self):
        """打开教师管理窗口"""
        if self.teacher_window is None or not self.teacher_window.window.winfo_exists():
            self.teacher_window = TeacherWindow(self.root)
        else:
            self.teacher_window.window.lift()
            self.teacher_window.window.focus()
    
    def open_student_window(self):
        """打开学生管理窗口"""
        if self.student_window is None or not self.student_window.window.winfo_exists():
            self.student_window = StudentWindow(self.root)
        else:
            self.student_window.window.lift()
            self.student_window.window.focus()
    
    def open_course_window(self):
        """打开课程管理窗口"""
        if self.course_window is None or not self.course_window.window.winfo_exists():
            self.course_window = CourseWindow(self.root)
        else:
            self.course_window.window.lift()
            self.course_window.window.focus()
    
    def open_sc_window(self):
        """打开选课管理窗口"""
        if self.sc_window is None or not self.sc_window.window.winfo_exists():
            self.sc_window = SCWindow(self.root)
        else:
            self.sc_window.window.lift()
            self.sc_window.window.focus()
    
    def exit_application(self):
        """退出应用程序"""
        if messagebox.askokcancel("确认退出", "确定要退出学生管理系统吗？"):
            self.root.quit()
    
    def run(self):
        """运行主窗口"""
        self.root.mainloop()
